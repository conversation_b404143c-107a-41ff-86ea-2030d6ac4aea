# DPPaaS 数字产品护照平台 - MVP技术栈方案

## 项目概述

**目标**: 快速构建MVP（最小可行产品），满足欧盟ESPR法规基础要求，能够过审上线，后续再考虑功能扩展和资质申请。

**核心原则**:
- 开发速度优先，过审为主要目标
- 成本控制，月成本控制在50美元以内
- 技术栈统一，降低学习成本
- 架构简单，便于快速迭代

## MVP最小功能集

### 核心功能（必须实现）
1. **产品注册管理** - 经营者注册产品，生成唯一UID
2. **DPP生成** - 生成数字产品护照JSON-LD格式数据
3. **二维码生成** - 为每个产品生成二维码
4. **数字签名** - 基础的ECDSA数字签名功能
5. **产品查询** - 通过UID查询产品信息
6. **用户认证** - 基础的用户登录注册

### 暂缓功能（后续版本）
- 复杂的VC（可验证凭证）支持
- 多租户架构
- 高级权限管理
- 审计日志
- 性能监控
- 微服务拆分

## MVP技术栈

### 前端技术栈（简化版）

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Next.js** | 14.x | React框架 | 全栈框架，API路由内置 |
| **TypeScript** | 5.x | 类型系统 | 类型安全，减少bug |
| **Tailwind CSS** | 3.x | CSS框架 | 快速UI开发 |
| **Shadcn/ui** | 最新 | 组件库 | 现成组件，快速搭建 |
| **React Hook Form** | 7.x | 表单处理 | 简单表单管理 |

### 后端技术栈（简化版）

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Next.js API Routes** | 14.x | 后端API | 与前端统一，减少复杂度 |
| **TypeScript** | 5.x | 类型系统 | 前后端类型统一 |
| **Prisma ORM** | 5.x | 数据库ORM | 简单易用，类型安全 |
| **NextAuth.js** | 4.x | 认证方案 | Next.js原生认证 |
| **qrcode** | 1.x | 二维码生成 | 轻量级二维码库 |
| **node-forge** | 1.x | 数字签名 | 基础加密功能 |

### 数据存储（最小化）

| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **AWS RDS PostgreSQL** | 主数据库 | 稳定可靠，JSONB支持 |
| **AWS S3** | 文件存储 | 产品图片存储 |

### AWS部署架构（成本优化）

| 服务 | 配置 | 用途 | 月成本估算 |
|------|------|------|----------|
| **EC2** | t3.micro | 应用服务器 | ~$8 |
| **RDS PostgreSQL** | t3.micro | 数据库 | ~$13 |
| **S3** | 标准存储 | 文件存储 | ~$3 |
| **Route 53** | 标准 | DNS管理 | ~$1 |
| **Certificate Manager** | 免费 | SSL证书 | $0 |
| **总计** | - | - | **~$25/月** |

### 开发工具（精简版）

| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **Docker** | 本地开发 | 环境一致性 |
| **GitHub** | 代码仓库 | 版本控制 |
| **Vercel** | 部署平台 | Next.js原生支持，简化部署 |
| **ESLint + Prettier** | 代码规范 | 代码质量 |

## MVP数据库设计

### 核心数据表（最小化）

```sql
-- 用户表
users (id, email, password_hash, name, role, created_at)

-- 产品表
products (id, uid, name, description, manufacturer, created_at, updated_at)

-- 数字产品护照表
digital_passports (id, product_id, passport_data, signature, created_at)

-- 二维码表
qr_codes (id, product_id, qr_data, created_at)
```

### 数据存储策略
- **PostgreSQL JSONB** - 存储DPP的JSON-LD数据
- **简单索引** - 基于UID和产品ID的查询优化
- **无分区** - MVP阶段数据量小，暂不分区

## MVP安全设计

### 基础数字签名
- **算法**: ECDSA_P256_SHA256
- **密钥存储**: 环境变量（MVP阶段）
- **签名格式**: 简单的JSON签名

### 基础认证
- **NextAuth.js** - 邮箱密码登录
- **JWT Token** - 会话管理
- **基础权限** - 管理员/普通用户两级

## MVP开发计划

### 第一阶段（2周）- 基础框架
1. Next.js项目初始化
2. 数据库设计和Prisma配置
3. 基础用户认证
4. 简单的产品CRUD

### 第二阶段（2周）- 核心功能
1. DPP JSON-LD生成
2. 数字签名功能
3. 二维码生成
4. 产品查询API

### 第三阶段（1周）- 部署上线
1. AWS环境配置
2. 生产环境部署
3. 基础测试
4. 域名和SSL配置

## 后续扩展路径

### V2版本功能
- 完整的VC（可验证凭证）支持
- 多租户架构
- 高级权限管理
- 审计日志

### V3版本功能
- 微服务拆分
- 性能优化
- 监控告警
- 多区域部署

## 成本控制

### 开发成本
- **开发时间**: 5周MVP开发
- **人力成本**: 1名全栈开发者
- **第三方服务**: GitHub + Vercel免费额度

### 运营成本
- **AWS月成本**: ~$25
- **域名成本**: ~$12/年
- **总月成本**: ~$26

这个MVP技术栈方案专注于快速实现核心功能，满足过审要求，同时为后续扩展预留了清晰的升级路径。
