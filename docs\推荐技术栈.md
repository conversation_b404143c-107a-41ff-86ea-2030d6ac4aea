# DPPaaS 数字产品护照平台 - MVP技术栈方案

## 基于需求分析的技术栈设计

### 业务需求分析（基于概括文档）

**核心业务场景**:
1. **生产环节**: 经营者注册→产品注册→DPP创建→二维码生成→VC管理
2. **清关环节**: 海关扫码→UID查询→DPP验证→VC验证
3. **平台管理**: 托管服务、数字签名、Registry集成

**关键技术要求**:
- **机器可读**: JSON-LD格式，40个核心字段
- **数字签名**: ECDSA_P256_SHA256算法
- **不可篡改**: 数字签名+哈希校验
- **VC支持**: W3C可验证凭证标准
- **载体支持**: 二维码、NFC、GS1数字连接

**用户角色**:
- 经营者（EORI号码注册）
- 第三方检测机构（VC签发）
- 海关（查询验证）
- 物流商（申报）
- DPPaaS平台（托管管理）

### 技术栈选择（基于软件技术文档）

根据软件技术文档的明确要求，采用以下技术栈：

## MVP技术栈设计

### 1. 前端技术栈 - Nuxt.js (Vue 3)

**选择理由**: 软件技术文档明确指定使用Nuxt.js作为前端框架

| 技术组件 | 版本 | 具体用途 | MVP实现要点 |
|---------|------|---------|------------|
| **Nuxt.js** | 3.x | Vue 3全栈框架 | SSR支持，可生成分享URL (/passport/{uid}) |
| **Vue 3** | 3.x | 前端框架 | Composition API，响应式数据管理 |
| **Tailwind CSS** | 3.x | CSS框架 | 快速UI开发，移动端适配 |
| **Pinia** | 2.x | 状态管理 | 轻量级状态管理，TypeScript支持 |
| **Nuxt UI** | 最新 | 组件库 | 现成组件，加速开发 |

**MVP核心页面**:
- 经营者注册/登录页面
- 产品注册向导（40个核心字段表单）
- DPP生成和预览页面
- 二维码生成页面
- 海关查询公开页面

### 2. 后端技术栈 - Go微服务

**选择理由**: 软件技术文档明确指定使用Go微服务架构

| 服务名称 | 核心功能 | 技术栈 | MVP范围 |
|---------|---------|--------|---------|
| **passport-service** | DPP CRUD、JSON-LD生成、数字签名 | Go + Gin + GORM | 核心服务，必须实现 |
| **registry-service** | 产品注册、UID管理、海关查询API | Go + Gin + Redis | 核心服务，必须实现 |
| **auth-service** | 用户认证、权限管理 | Go + Keycloak | 简化版，基础认证 |
| **compliance-service** | VC管理、合规校验 | Go + 加密库 | 简化版，基础VC支持 |
| **notification-service** | 消息通知 | Go + WebSocket | 可选，后期添加 |

**Go技术栈配置**:
```go
// 核心依赖包
- Go 1.21+
- Gin Web Framework v1.9+
- GORM v1.25+ (PostgreSQL驱动)
- go-redis v9+ (Redis客户端)
- crypto/ecdsa (数字签名)
- encoding/json (JSON-LD处理)
```

### 3. 数据存储技术栈

**选择理由**: 软件技术文档明确指定使用Aurora PostgreSQL

| 技术 | 用途 | MVP配置 | 选择理由 |
|------|------|---------|----------|
| **Aurora PostgreSQL** | 主数据库 | 单实例，基础配置 | JSONB支持，适合DPP数据 |
| **Redis** | 缓存/会话 | 单节点 | 高性能缓存，会话存储 |
| **S3** | 文件存储 | 标准存储 | 产品图片、证书文件 |

### 4. 用户认证系统 - Keycloak

**选择理由**: 软件技术文档明确推荐Keycloak

| 功能 | MVP实现 | 配置 |
|------|---------|------|
| **多租户认证** | 基础租户支持 | 单Realm配置 |
| **RBAC权限** | 3个基础角色 | 经营者、检测机构、管理员 |
| **OIDC集成** | 标准OIDC流程 | 与Go服务集成 |

### 5. 事件系统 - Apache Kafka (简化版)

**选择理由**: 软件技术文档明确指定使用Kafka

| Topic | 用途 | MVP范围 |
|-------|------|---------|
| **passport_events** | DPP创建/更新事件 | 基础事件发布 |
| **compliance_events** | VC验证事件 | 简化版事件 |

**MVP阶段简化**: 使用单节点Kafka，基础事件发布订阅

### 2. API/业务层 - Go 微服务

| 服务名称 | 关键功能 | 对接外部 | 技术栈 |
|---------|---------|---------|--------|
| **passport-service** | CRUD、版本比对、生成QR/NFC payload；写Aurora；发passport_created/updated事件到Kafka | REST/gRPC | Go + Gin + GORM |
| **registry-service** | 统一ProductID⇄PassportID解析、公开查询API；对外限流&审计 | REST | Go + Gin + Redis |
| **compliance-service** | 校验供应链证书、碳足迹算法（ISO 14067）；异步校验任务入Kafka | gRPC/Async job | Go + 加密库 |
| **auth-service** | OIDC provider/租户RBAC；签JWT，供Nuxt&其他服务验证 | OIDC/gRPC | Go + Keycloak集成 |
| **notification-service** | 监听Kafka事件→推邮件、WebSocket、SMS | Kafka/REST | Go + WebSocket |

#### Go技术栈详细配置
```go
// 核心依赖
- Go 1.21+
- Gin Web Framework v1.9+
- GORM v1.25+ (PostgreSQL驱动)
- Kafka Go Client (Sarama)
- JWT-Go v5+
- Crypto库 (ECDSA_P256_SHA256)
- Protobuf + gRPC
```

### 3. 数据层 - Amazon Aurora (PostgreSQL)

| 模块 | 存储内容 | 技术特点 |
|------|---------|---------|
| **Core DB** | passports, product_items, suppliers, audits, users, tenants | ACID、跨AZ高可用，支持JSONB存半结构化属性 |
| **History DB/分区表** | 护照历史、变更快照，按月RANGE + PARTITION | 便于GDPR删除 |
| **Read Replicas** | 供报告/BI、全文索引任务 | 隔离OLAP读压 |
| **Logical Replication** | 变更流灌入Kafka（Debezium）→ Elasticsearch近实时搜索 | CDC数据流 |

### 4. 事件&集成层 - Apache Kafka

| Topic范围 | 发布者 | 订阅者 | 用途 |
|----------|--------|--------|------|
| passport_created, passport_updated | passport-service | notification-service, search-indexer | 更新UI、推送、重建索引 |
| compliance_check_requested | Nuxt调用Go API→compliance-service | compliance-service(worker) | 触发异步校验 |
| compliance_check_result | compliance-service | passport-service, notification-service | 写回结果、提醒审核人 |
| cdc_coredb | Debezium (Aurora) | analytics-pipeline | 数据湖增量拉链、BI报表 |
| external_webhook_out | 各Go服务 | partner-gateway | 向外部ERP/PLM广播 |

### 5. 用户系统 - Keycloak

| 功能模块 | 实现方式 | 选择理由 |
|---------|---------|---------|
| **多租户认证** | Keycloak Realms | 完全掌控、EU数据驻留 |
| **RBAC权限** | Keycloak Roles + Groups | 插件细节多，自己打补丁与监控 |
| **OIDC集成** | 标准OIDC协议 | 与Go服务无缝集成 |
| **企业SSO** | SAML/OIDC连接器 | 支持企业级身份提供商 |

## 产品架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        DPPaaS 平台架构                          │
├─────────────────────────────────────────────────────────────────┤
│  表现层 (Presentation Layer)                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   经营者门户     │  │   海关查询界面   │  │   第三方机构     │  │
│  │   (Nuxt.js)     │  │   (Public API)  │  │   (API集成)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  API网关层 (API Gateway)                                        │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │           Nginx/Kong API Gateway + 限流 + 认证              │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  业务服务层 (Business Services - Go微服务)                      │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌─────────┐ │
│  │passport-svc  │ │registry-svc  │ │compliance-svc│ │auth-svc │ │
│  │DPP核心管理   │ │产品注册查询  │ │合规性校验    │ │认证授权 │ │
│  └──────────────┘ └──────────────┘ └──────────────┘ └─────────┘ │
│  ┌──────────────┐ ┌──────────────┐                              │
│  │notification  │ │file-service  │                              │
│  │消息通知服务  │ │文件管理服务  │                              │
│  └──────────────┘ └──────────────┘                              │
├─────────────────────────────────────────────────────────────────┤
│  事件总线层 (Event Bus)                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Apache Kafka 事件流                          │ │
│  │  passport_events | compliance_events | notification_events  │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                            │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌─────────┐ │
│  │Aurora主库    │ │Aurora只读副本│ │Redis缓存     │ │S3存储   │ │
│  │核心业务数据  │ │报表查询      │ │会话/临时数据 │ │文件资源 │ │
│  └──────────────┘ └──────────────┘ └──────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  外部集成层 (External Integration)                              │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐             │
│  │DPP Registry  │ │第三方检测机构│ │ERP/PLM系统   │             │
│  │欧委会注册库  │ │VC签发机构    │ │企业系统集成  │             │
│  └──────────────┘ └──────────────┘ └──────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```

### 核心数据模型设计

#### 1. 经营者管理模型
```sql
-- 经营者表 (符合EORI号码管理要求)
CREATE TABLE economic_operators (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  eori_number VARCHAR(17) UNIQUE NOT NULL,  -- EORI号码 (GB**********00)
  company_name VARCHAR(255) NOT NULL,
  legal_entity_type VARCHAR(50),  -- 'corporation', 'partnership', 'sole_proprietorship'
  registration_country VARCHAR(2),  -- ISO 3166-1 alpha-2
  contact_email VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(50),
  business_address JSONB NOT NULL,  -- 完整地址信息
  vat_number VARCHAR(50),
  business_license VARCHAR(100),
  certification_status VARCHAR(20) DEFAULT 'pending',  -- 'pending', 'verified', 'suspended'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. 产品管理模型
```sql
-- 产品表 (支持UID生成和40个核心字段)
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  uid VARCHAR(255) UNIQUE NOT NULL,  -- 产品唯一标识符 (iso15559:ACME:**********)
  economic_operator_id UUID REFERENCES economic_operators(id),

  -- 基础产品信息
  product_name VARCHAR(255) NOT NULL,
  product_category VARCHAR(100) NOT NULL,  -- 对应11个首批行业
  manufacturer VARCHAR(255) NOT NULL,
  model_number VARCHAR(100),
  batch_number VARCHAR(100),
  production_date DATE,
  production_location JSONB,

  -- 40个核心字段 (JSON-LD格式存储)
  core_attributes JSONB NOT NULL,  -- 包含材料、尺寸、重量、能效等
  industry_specific JSONB,  -- 行业特定字段

  -- 供应链信息
  supply_chain_info JSONB,  -- 供应链节点信息

  -- 环保信息
  carbon_footprint DECIMAL(10,3),  -- 碳足迹 (kg CO2e)
  recyclability_rate DECIMAL(5,2),  -- 可回收率 (%)
  energy_efficiency_class VARCHAR(10),  -- 能效等级

  -- 状态管理
  status VARCHAR(20) DEFAULT 'draft',  -- 'draft', 'active', 'suspended', 'recalled'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. DPP核心模型
```sql
-- 数字产品护照表 (JSON-LD格式 + 数字签名)
CREATE TABLE digital_passports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),

  -- DPP Registry相关
  service_provider_id VARCHAR(100) NOT NULL,  -- DPPaaS平台标识
  dpp_endpoint VARCHAR(500) NOT NULL,  -- DPP数据访问端点
  data_carrier_digest VARCHAR(255),  -- 加密指纹验证

  -- JSON-LD数据
  passport_data JSONB NOT NULL,  -- 完整的JSON-LD格式DPP数据
  context_urls TEXT[],  -- @context URLs

  -- 数字签名 (ECDSA_P256_SHA256)
  signature TEXT NOT NULL,  -- 数字签名
  signature_algorithm VARCHAR(50) DEFAULT 'ECDSA_P256_SHA256',
  public_key TEXT NOT NULL,  -- 公钥
  signing_date TIMESTAMP DEFAULT NOW(),

  -- 版本管理
  version INTEGER DEFAULT 1,
  previous_version_id UUID REFERENCES digital_passports(id),

  -- 状态管理
  status VARCHAR(20) DEFAULT 'active',  -- 'active', 'revoked', 'expired'
  valid_until TIMESTAMP,

  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. VC管理模型
```sql
-- 可验证凭证表 (第三方检测机构签发)
CREATE TABLE verifiable_credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),

  -- VC基础信息
  credential_id VARCHAR(255) UNIQUE NOT NULL,  -- VC唯一标识
  issuer_did VARCHAR(255) NOT NULL,  -- 签发机构DID
  issuer_name VARCHAR(255) NOT NULL,  -- 签发机构名称
  credential_type VARCHAR(100) NOT NULL,  -- 'CertificationCredential', 'TestReportCredential'

  -- VC数据
  credential_data JSONB NOT NULL,  -- 完整的VC JSON-LD数据
  credential_subject JSONB NOT NULL,  -- credentialSubject内容

  -- 状态管理 (BitstringStatusList2021)
  status_list_index INTEGER,
  status_list_credential VARCHAR(500),
  revocation_reason VARCHAR(255),

  -- 数字签名证明
  proof JSONB NOT NULL,  -- 完整的proof对象

  -- 有效期管理
  valid_from TIMESTAMP NOT NULL,
  valid_until TIMESTAMP,

  -- 状态
  status VARCHAR(20) DEFAULT 'active',  -- 'active', 'revoked', 'expired', 'suspended'

  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 5. 载体管理模型
```sql
-- 数据载体表 (二维码/NFC/条形码)
CREATE TABLE data_carriers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id),

  -- 载体信息
  carrier_type VARCHAR(20) NOT NULL,  -- 'qr_code', 'nfc', 'barcode', 'rfid'
  carrier_format VARCHAR(50),  -- 'QR_CODE_2D', 'NFC_TYPE_A', 'CODE_128'

  -- 载体数据
  carrier_data TEXT NOT NULL,  -- 二维码内容或NFC数据
  encoded_url VARCHAR(500),  -- 编码后的访问URL

  -- GS1数字连接支持
  gtin VARCHAR(14),  -- 全球贸易项目代码
  serial_number VARCHAR(50),  -- 序列号
  gs1_digital_link VARCHAR(500),  -- GS1数字连接URL

  -- 物理属性
  size_mm VARCHAR(20),  -- 载体尺寸 (如: "25x25")
  print_quality VARCHAR(20),  -- 打印质量等级

  -- 生成信息
  generated_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,  -- 载体过期时间

  created_at TIMESTAMP DEFAULT NOW()
);
```

### 核心业务流程设计

#### 1. DPP生成流程 (Go实现)
```go
// DPP核心字段结构 (40个必需字段)
type DPPCoreFields struct {
    // 基础标识信息
    UID              string    `json:"uid"`
    ProductName      string    `json:"productName"`
    Manufacturer     string    `json:"manufacturer"`
    ModelNumber      string    `json:"modelNumber"`
    BatchNumber      string    `json:"batchNumber"`
    ProductionDate   time.Time `json:"productionDate"`

    // 材料信息
    Materials        []MaterialInfo `json:"materials"`
    RecycleRate      float64       `json:"recycleRate"`

    // 环保信息
    CarbonFootprint  float64 `json:"carbonFootprint"`
    EnergyEfficiency string  `json:"energyEfficiency"`

    // 合规信息
    Certifications   []Certification `json:"certifications"`
    ComplianceStatus string         `json:"complianceStatus"`

    // 供应链信息
    SupplyChainInfo  []SupplyChainNode `json:"supplyChainInfo"`

    // 其他核心字段...
}

// JSON-LD DPP文档结构
type DPPDocument struct {
    Context          []string        `json:"@context"`
    ID               string          `json:"id"`
    Type             []string        `json:"type"`
    CredentialSubject DPPCoreFields  `json:"credentialSubject"`
    Issuer           string          `json:"issuer"`
    IssuanceDate     time.Time       `json:"issuanceDate"`
    Proof            DigitalSignature `json:"proof"`
}

// ECDSA_P256_SHA256数字签名
type DigitalSignature struct {
    Type               string    `json:"type"`
    Created            time.Time `json:"created"`
    VerificationMethod string    `json:"verificationMethod"`
    ProofPurpose       string    `json:"proofPurpose"`
    ProofValue         string    `json:"proofValue"`
}
```

#### 2. 数字签名实现 (Go + crypto库)
```go
package signature

import (
    "crypto/ecdsa"
    "crypto/elliptic"
    "crypto/rand"
    "crypto/sha256"
    "encoding/base64"
    "encoding/json"
)

// ECDSA_P256_SHA256签名服务
type ECDSASignatureService struct {
    privateKey *ecdsa.PrivateKey
    publicKey  *ecdsa.PublicKey
}

// 生成DPP数字签名
func (s *ECDSASignatureService) SignDPP(dppData DPPCoreFields) (*DigitalSignature, error) {
    // 1. 规范化JSON-LD数据
    canonicalData, err := s.canonicalizeJSONLD(dppData)
    if err != nil {
        return nil, err
    }

    // 2. 生成SHA256哈希
    hash := sha256.Sum256(canonicalData)

    // 3. ECDSA签名
    r, s, err := ecdsa.Sign(rand.Reader, s.privateKey, hash[:])
    if err != nil {
        return nil, err
    }

    // 4. 编码签名
    signature := append(r.Bytes(), s.Bytes()...)
    proofValue := base64.StdEncoding.EncodeToString(signature)

    return &DigitalSignature{
        Type:               "ECDSA_P256_SHA256",
        Created:            time.Now(),
        VerificationMethod: s.getPublicKeyDID(),
        ProofPurpose:       "assertionMethod",
        ProofValue:         proofValue,
    }, nil
}

// 验证DPP数字签名
func (s *ECDSASignatureService) VerifyDPP(dppDoc DPPDocument) (bool, error) {
    // 实现签名验证逻辑
    // ...
}
```

#### 3. VC管理流程
```go
// 可验证凭证结构 (W3C标准)
type VerifiableCredential struct {
    Context          []string    `json:"@context"`
    ID               string      `json:"id"`
    Type             []string    `json:"type"`
    Issuer           string      `json:"issuer"`  // 第三方检测机构DID
    CredentialSubject struct {
        ID               string      `json:"id"`  // 产品UID
        CertificationData interface{} `json:"certificationData"`
    } `json:"credentialSubject"`
    CredentialStatus struct {
        Type                  string `json:"type"`  // "BitstringStatusList2021"
        StatusListIndex       string `json:"statusListIndex"`
        StatusListCredential  string `json:"statusListCredential"`
    } `json:"credentialStatus"`
    Proof            DigitalSignature `json:"proof"`
}

// VC验证服务
type VCVerificationService struct {
    trustedIssuers map[string]bool  // 可信签发机构列表
}

func (s *VCVerificationService) VerifyVC(vc VerifiableCredential) (*VerificationResult, error) {
    // 1. 验证签发机构是否可信
    if !s.trustedIssuers[vc.Issuer] {
        return nil, errors.New("untrusted issuer")
    }

    // 2. 验证数字签名
    valid, err := s.verifySignature(vc)
    if err != nil || !valid {
        return nil, errors.New("invalid signature")
    }

    // 3. 检查撤销状态
    revoked, err := s.checkRevocationStatus(vc.CredentialStatus)
    if err != nil || revoked {
        return nil, errors.New("credential revoked")
    }

    return &VerificationResult{Valid: true}, nil
}
```

## 明确的API设计规范

### Go微服务API端点设计

#### 1. passport-service (DPP核心服务)
```go
// RESTful API端点
POST   /v1/passports                    // 创建DPP
GET    /v1/passports/:uid               // 通过UID获取DPP (海关查询)
PUT    /v1/passports/:id                // 更新DPP
DELETE /v1/passports/:id               // 删除DPP
GET    /v1/passports/:id/verify         // 验证DPP完整性
POST   /v1/passports/:id/sign           // 重新签名DPP
GET    /v1/passports/:id/history        // 获取DPP版本历史

// gRPC接口
service PassportService {
    rpc CreatePassport(CreatePassportRequest) returns (PassportResponse);
    rpc GetPassport(GetPassportRequest) returns (PassportResponse);
    rpc UpdatePassport(UpdatePassportRequest) returns (PassportResponse);
    rpc VerifyPassport(VerifyPassportRequest) returns (VerificationResponse);
}
```

#### 2. registry-service (产品注册服务)
```go
// 产品注册API
POST   /v1/products                     // 注册产品
GET    /v1/products/:uid                // 通过UID查询产品
PUT    /v1/products/:id                 // 更新产品信息
GET    /v1/products/search              // 产品搜索
POST   /v1/products/:id/uid/generate    // 生成产品UID

// 经营者管理API
POST   /v1/operators                    // 注册经营者
GET    /v1/operators/:eori              // 通过EORI查询经营者
PUT    /v1/operators/:id                // 更新经营者信息
POST   /v1/operators/:id/verify         // 验证经营者资质
```

#### 3. compliance-service (合规校验服务)
```go
// VC管理API
POST   /v1/credentials                  // 上传VC
GET    /v1/credentials/:productId       // 获取产品相关VC
PUT    /v1/credentials/:id/status       // 更新VC状态
POST   /v1/credentials/:id/verify       // 验证VC
GET    /v1/credentials/:id/revoke       // 撤销VC

// 合规校验API
POST   /v1/compliance/check             // 触发合规检查
GET    /v1/compliance/status/:jobId     // 查询检查状态
GET    /v1/compliance/report/:productId // 获取合规报告
```

#### 4. 载体生成服务
```go
// 载体生成API
POST   /v1/carriers/qr                  // 生成二维码
POST   /v1/carriers/nfc                 // 生成NFC数据
POST   /v1/carriers/barcode             // 生成条形码
GET    /v1/carriers/:productId          // 获取产品所有载体
POST   /v1/carriers/gs1                 // 生成GS1数字连接
```

### DPP Registry集成接口
```go
// 与欧委会DPP Registry的集成接口
POST   /v1/registry/register            // 向DPP Registry注册
PUT    /v1/registry/update              // 更新Registry信息
GET    /v1/registry/status              // 查询注册状态
POST   /v1/registry/sync                // 同步数据到Registry
```

## 部署架构设计

### AWS生产环境架构 (基于软件技术文档)
```
┌─────────────────────────────────────────────────────────────────┐
│                     AWS部署架构                                 │
├─────────────────────────────────────────────────────────────────┤
│  CDN层: CloudFront + S3                                        │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  静态资源缓存 + 全球边缘节点加速                              │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  负载均衡层: Application Load Balancer                          │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  SSL终止 + 健康检查 + 路由规则                               │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  应用层: ECS Fargate                                            │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌─────────┐ │
│  │Nuxt.js前端   │ │passport-svc  │ │registry-svc  │ │auth-svc │ │
│  │Fargate Task  │ │Fargate Task  │ │Fargate Task  │ │Keycloak │ │
│  └──────────────┘ └──────────────┘ └──────────────┘ └─────────┘ │
│  ┌──────────────┐ ┌──────────────┐                              │
│  │compliance-svc│ │notification  │                              │
│  │Fargate Task  │ │Fargate Task  │                              │
│  └──────────────┘ └──────────────┘                              │
├─────────────────────────────────────────────────────────────────┤
│  消息队列: MSK Serverless (Kafka)                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │  事件流处理 + 服务解耦 + 异步任务                            │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  数据层: Aurora Serverless v2 + ElastiCache                    │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────┐             │
│  │Aurora主库    │ │Aurora只读副本│ │Redis缓存     │             │
│  │PostgreSQL    │ │报表查询      │ │会话/临时数据 │             │
│  └──────────────┘ └──────────────┘ └──────────────┘             │
├─────────────────────────────────────────────────────────────────┤
│  存储层: S3 + EFS                                               │
│  ┌──────────────┐ ┌──────────────┐                              │
│  │S3存储        │ │EFS共享存储   │                              │
│  │文件/证书     │ │容器共享文件  │                              │
│  └──────────────┘ └──────────────┘                              │
└─────────────────────────────────────────────────────────────────┘
```

### Terraform基础设施即代码
```hcl
# terraform/main.tf
provider "aws" {
  region = "eu-west-1"  # 欧洲区域部署
}

# ECS Fargate集群
resource "aws_ecs_cluster" "dppaas_cluster" {
  name = "dppaas-cluster"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# Aurora Serverless v2数据库
resource "aws_rds_cluster" "dppaas_db" {
  cluster_identifier      = "dppaas-aurora"
  engine                 = "aurora-postgresql"
  engine_mode            = "provisioned"
  engine_version         = "15.4"
  database_name          = "dppaas"
  master_username        = "dppaas_admin"
  master_password        = var.db_password

  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 0.5
  }
}

# MSK Serverless Kafka
resource "aws_msk_serverless_cluster" "dppaas_kafka" {
  cluster_name = "dppaas-kafka"

  vpc_config {
    subnet_ids = var.private_subnet_ids
  }
}
```

### GitHub Actions CI/CD流水线
```yaml
# .github/workflows/deploy.yml
name: DPPaaS Deploy Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      # Go微服务测试
      - name: Test Go Services
        run: |
          cd services
          go mod tidy
          go test ./...

      # Nuxt.js前端测试
      - name: Test Frontend
        run: |
          cd frontend
          npm ci
          npm run test
          npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      # 部署到AWS ECS Fargate
      - name: Deploy to AWS
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        run: |
          terraform init
          terraform plan
          terraform apply -auto-approve
```

## 明确的开发计划

### 第一阶段（4周）- 基础架构搭建

#### Week 1: 项目初始化
- **Go微服务框架搭建**（2天）
  - 设置Go项目结构，配置Gin框架
  - 实现基础中间件（日志、CORS、认证）
  - 配置gRPC服务间通信

- **Nuxt.js前端初始化**（2天）
  - 创建Nuxt 3项目，配置Tailwind CSS
  - 设置Pinia状态管理
  - 实现基础布局和路由

- **数据库设计实现**（1天）
  - 使用GORM实现数据模型
  - 创建数据库迁移脚本
  - 配置Aurora PostgreSQL连接

#### Week 2-3: 核心服务开发
- **passport-service开发**（5天）
  - 实现DPP CRUD操作
  - JSON-LD格式生成
  - ECDSA数字签名集成

- **registry-service开发**（4天）
  - 产品注册管理
  - UID生成算法
  - 查询接口实现

- **auth-service + Keycloak集成**（3天）
  - Keycloak配置和集成
  - JWT令牌验证
  - RBAC权限控制

#### Week 4: 前端核心功能
- **经营者管理界面**（3天）
  - EORI号码注册表单
  - 经营者信息管理
  - 权限控制界面

- **产品管理界面**（2天）
  - 产品注册向导
  - 40个核心字段表单
  - 产品列表和搜索

### 第二阶段（3周）- 核心业务功能

#### Week 5: DPP生成系统
- **DPP生成引擎**（4天）
  - 完整的JSON-LD格式实现
  - 数字签名服务完善
  - 数据完整性验证

- **载体生成服务**（3天）
  - 二维码生成（支持GS1数字连接）
  - NFC数据生成
  - 载体管理界面

#### Week 6: VC管理系统
- **compliance-service开发**（4天）
  - VC上传和验证
  - BitstringStatusList2021状态管理
  - 第三方机构集成接口

- **VC管理界面**（3天）
  - VC上传界面
  - VC状态监控
  - 验证结果展示

#### Week 7: 查询和集成
- **海关查询接口**（3天）
  - 公开查询API
  - 限流和安全控制
  - 查询结果缓存

- **DPP Registry集成**（4天）
  - 与欧委会系统对接
  - 数据同步机制
  - 错误处理和重试

### 第三阶段（2周）- 部署和测试

#### Week 8: 生产部署
- **AWS基础设施部署**（3天）
  - Terraform脚本完善
  - ECS Fargate服务部署
  - Aurora和MSK配置

- **监控和告警**（2天）
  - CloudWatch监控配置
  - 告警规则设置
  - 日志聚合配置

#### Week 9: 测试和优化
- **端到端测试**（3天）
  - 完整业务流程测试
  - 性能压力测试
  - 安全渗透测试

- **用户验收测试**（2天）
  - 用户界面优化
  - 业务流程验证
  - 文档完善

## 总结

这个技术栈和产品架构方案完全基于概括文档和软件技术文档的要求设计：

### 核心特点
1. **完整的业务覆盖**: 从经营者注册到海关查询的全流程支持
2. **明确的技术栈**: Nuxt.js + Go微服务 + Aurora + Kafka + Keycloak
3. **符合欧盟标准**: JSON-LD格式、ECDSA_P256_SHA256签名、VC管理
4. **生产级架构**: AWS Fargate + Aurora Serverless v2 + MSK Serverless
5. **详细的实现方案**: 包含数据模型、API设计、部署配置

### 开发周期
- **总计9周**: 4周基础架构 + 3周核心功能 + 2周部署测试
- **可扩展性**: 微服务架构便于后续功能扩展
- **合规性**: 严格按照ESPR法规要求实现

这个方案能够快速构建出符合过审要求的DPPaaS平台MVP，同时为后续的功能扩展和规模化运营奠定坚实基础。
