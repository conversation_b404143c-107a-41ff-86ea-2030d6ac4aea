# DPPaaS 数字产品护照平台 - MVP技术栈与架构方案

## 项目概述

**目标**: 构建符合欧盟ESPR法规要求的DPPaaS平台MVP，实现数字产品护照托管服务，满足过审要求。

**业务定位**:
- 数字产品护照托管平台（DPP as a Service）
- 为经营者提供DPP注册、生成、管理服务
- 支持海关、第三方机构的DPP查询验证

**核心价值**:
- 拥有资质的数字产品护照托管平台
- 数字身份颁发，数字签名功能实现
- 通过销售"护照（二维码）"及护照管理收费

## 基于业务场景的MVP功能定义

### 生产环节功能
1. **经营者注册管理** - 支持EORI号码注册
2. **产品注册** - 创建产品基础信息，生成唯一UID
3. **DPP创建** - 生成包含40个核心字段的JSON-LD格式DPP
4. **二维码/NFC生成** - 为产品生成可扫描的载体
5. **VC管理** - 上传和管理第三方检测机构的可验证凭证
6. **数字签名** - ECDSA_P256_SHA256算法签名确保数据完整性

### 清关环节功能
1. **UID查询接口** - 海关通过UID查询DPP有效性
2. **DPP数据提供** - 向授权机构提供完整DPP数据
3. **VC验证** - 验证可验证凭证的完整性和有效性
4. **DPP Registry集成** - 与欧委会DPP注册库的数据同步

### 平台管理功能
1. **用户权限管理** - 经营者、第三方机构、管理员角色
2. **数据完整性保障** - 防篡改机制
3. **服务提供商ID管理** - serviceProviderId分配
4. **数据迁移支持** - 托管者变更时的数据迁移

## 技术架构设计

### 整体架构模式
- **单体应用架构** - MVP阶段采用单体应用，快速开发部署
- **前后端分离** - 前端SPA + 后端API，支持多端访问
- **RESTful API** - 标准REST接口，便于第三方集成
- **事件驱动** - 关键业务操作采用事件机制

### 核心技术栈

#### 前端技术栈

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Next.js** | 14.x | React全栈框架 | SSR支持，API路由内置，开发效率高 |
| **TypeScript** | 5.x | 类型系统 | 类型安全，减少运行时错误 |
| **Tailwind CSS** | 3.x | CSS框架 | 快速UI开发，响应式设计 |
| **Shadcn/ui** | 最新 | 组件库 | 现成组件，符合现代设计规范 |
| **React Hook Form** | 7.x | 表单处理 | 高性能表单验证和管理 |
| **React Query** | 5.x | 数据获取 | 缓存和同步服务端状态 |

#### 后端技术栈

| 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|----------|
| **Node.js** | 20 LTS | 运行时环境 | 与前端技术栈统一，生态丰富 |
| **Express.js** | 4.x | Web框架 | 成熟稳定，中间件丰富 |
| **TypeScript** | 5.x | 类型系统 | 前后端类型统一 |
| **Prisma ORM** | 5.x | 数据库ORM | 类型安全，迁移管理便捷 |
| **Passport.js** | 0.7.x | 认证中间件 | 多策略认证支持 |
| **jsonwebtoken** | 9.x | JWT处理 | 无状态认证 |
| **qrcode** | 1.x | 二维码生成 | 轻量级二维码库 |
| **node-forge** | 1.x | 数字签名 | ECDSA签名算法支持 |
| **ajv** | 8.x | JSON Schema验证 | JSON-LD数据验证 |

#### 数据存储技术栈

| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **PostgreSQL** | 主数据库 | JSONB支持，ACID特性，适合DPP数据存储 |
| **Redis** | 缓存/会话 | 高性能缓存，会话存储 |
| **文件存储** | 静态资源 | 产品图片、证书文件存储 |

#### 开发工具链

| 技术 | 用途 | 选择理由 |
|------|------|----------|
| **Docker** | 容器化 | 环境一致性，便于部署 |
| **GitHub** | 代码仓库 | 版本控制，CI/CD集成 |
| **GitHub Actions** | CI/CD | 自动化测试和部署 |
| **ESLint + Prettier** | 代码规范 | 代码质量保证 |
| **Jest** | 单元测试 | 测试覆盖率保证 |

## 数据库设计

### 核心数据模型

```sql
-- 经营者表
economic_operators (
  id UUID PRIMARY KEY,
  eori_number VARCHAR(17) UNIQUE NOT NULL,  -- EORI号码
  company_name VARCHAR(255) NOT NULL,
  contact_email VARCHAR(255) NOT NULL,
  contact_phone VARCHAR(50),
  address JSONB,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 产品表
products (
  id UUID PRIMARY KEY,
  uid VARCHAR(255) UNIQUE NOT NULL,  -- 产品唯一标识符
  economic_operator_id UUID REFERENCES economic_operators(id),
  product_name VARCHAR(255) NOT NULL,
  product_category VARCHAR(100),
  manufacturer VARCHAR(255),
  model_number VARCHAR(100),
  batch_number VARCHAR(100),
  production_date DATE,
  core_attributes JSONB,  -- 40个核心字段
  industry_specific JSONB,  -- 行业特定字段
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 数字产品护照表
digital_passports (
  id UUID PRIMARY KEY,
  product_id UUID REFERENCES products(id),
  service_provider_id VARCHAR(100) NOT NULL,  -- serviceProviderId
  passport_data JSONB NOT NULL,  -- JSON-LD格式的DPP数据
  data_carrier_digest VARCHAR(255),  -- 加密指纹
  dpp_endpoint VARCHAR(500),  -- DPP数据访问端点
  signature TEXT,  -- 数字签名
  signature_algorithm VARCHAR(50) DEFAULT 'ECDSA_P256_SHA256',
  public_key TEXT,
  version INTEGER DEFAULT 1,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 可验证凭证表
verifiable_credentials (
  id UUID PRIMARY KEY,
  product_id UUID REFERENCES products(id),
  issuer_did VARCHAR(255) NOT NULL,  -- 签发机构DID
  credential_type VARCHAR(100) NOT NULL,
  credential_data JSONB NOT NULL,  -- VC数据
  status_list_index INTEGER,
  status_list_credential VARCHAR(500),
  proof JSONB NOT NULL,  -- 数字签名证明
  valid_from TIMESTAMP,
  valid_until TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 二维码/载体表
data_carriers (
  id UUID PRIMARY KEY,
  product_id UUID REFERENCES products(id),
  carrier_type VARCHAR(20) NOT NULL,  -- 'qr_code', 'nfc', 'barcode'
  carrier_data TEXT NOT NULL,  -- 二维码内容或NFC数据
  format VARCHAR(50),  -- 格式信息
  created_at TIMESTAMP DEFAULT NOW()
);

-- 用户表
users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,  -- 'admin', 'operator', 'inspector'
  economic_operator_id UUID REFERENCES economic_operators(id),
  permissions JSONB,
  last_login TIMESTAMP,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 索引策略

```sql
-- 产品查询优化
CREATE INDEX idx_products_uid ON products(uid);
CREATE INDEX idx_products_operator ON products(economic_operator_id);
CREATE INDEX idx_products_category ON products(product_category);

-- DPP查询优化
CREATE INDEX idx_dpp_product ON digital_passports(product_id);
CREATE INDEX idx_dpp_endpoint ON digital_passports(dpp_endpoint);
CREATE INDEX idx_dpp_status ON digital_passports(status);

-- VC查询优化
CREATE INDEX idx_vc_product ON verifiable_credentials(product_id);
CREATE INDEX idx_vc_issuer ON verifiable_credentials(issuer_did);
CREATE INDEX idx_vc_status ON verifiable_credentials(status);

-- 载体查询优化
CREATE INDEX idx_carriers_product ON data_carriers(product_id);
CREATE INDEX idx_carriers_type ON data_carriers(carrier_type);
```

## 核心业务逻辑设计

### DPP生成流程

```typescript
interface DPPCoreFields {
  // 基础标识信息
  uid: string;                    // 产品唯一标识符
  productName: string;           // 产品名称
  manufacturer: string;          // 制造商
  modelNumber: string;           // 型号
  batchNumber: string;           // 批次号
  productionDate: string;        // 生产日期

  // 材料信息
  materials: MaterialInfo[];     // 材料组成
  recycleRate: number;          // 回收率

  // 环保信息
  carbonFootprint: number;      // 碳足迹
  energyEfficiency: string;     // 能效等级

  // 合规信息
  certifications: Certification[]; // 认证信息
  complianceStatus: string;     // 合规状态

  // 供应链信息
  supplyChainInfo: SupplyChainNode[]; // 供应链节点

  // 其他核心字段...
}

interface DPPDocument {
  "@context": string[];
  "id": string;
  "type": string[];
  "credentialSubject": DPPCoreFields;
  "issuer": string;
  "issuanceDate": string;
  "proof": DigitalSignature;
}
```

### 数字签名实现

```typescript
interface DigitalSignature {
  type: "ECDSA_P256_SHA256";
  created: string;
  verificationMethod: string;
  proofPurpose: "assertionMethod";
  proofValue: string;
}

// 签名生成流程
function generateDPPSignature(dppData: DPPCoreFields): DigitalSignature {
  // 1. 规范化JSON-LD数据
  const canonicalData = canonicalize(dppData);

  // 2. 生成SHA256哈希
  const hash = sha256(canonicalData);

  // 3. ECDSA签名
  const signature = ecdsaSign(hash, privateKey);

  return {
    type: "ECDSA_P256_SHA256",
    created: new Date().toISOString(),
    verificationMethod: publicKeyDID,
    proofPurpose: "assertionMethod",
    proofValue: base64Encode(signature)
  };
}
```

### VC管理机制

```typescript
interface VerifiableCredential {
  "@context": string[];
  "id": string;
  "type": string[];
  "issuer": string;              // 第三方检测机构DID
  "credentialSubject": {
    "id": string;                // 产品UID
    "certificationData": any;    // 认证数据
  };
  "credentialStatus": {
    "type": "BitstringStatusList2021";
    "statusListIndex": string;
    "statusListCredential": string;
  };
  "proof": DigitalSignature;
}
```

### 安全设计

#### 数字签名安全
- **算法**: ECDSA_P256_SHA256（符合欧盟标准）
- **密钥管理**: 硬件安全模块（HSM）或密钥管理服务
- **密钥轮换**: 定期密钥更新机制
- **签名验证**: 多重验证机制

#### 数据完整性保护
- **防篡改**: 数字签名 + 哈希校验
- **版本控制**: DPP数据版本管理
- **审计日志**: 所有操作记录
- **备份恢复**: 数据备份和恢复机制

#### 访问控制
- **角色权限**: 基于角色的访问控制（RBAC）
- **API认证**: JWT Token + API Key
- **数据加密**: 传输加密（TLS 1.3）+ 存储加密
- **审计追踪**: 用户操作审计

## API设计规范

### 核心API端点

#### 经营者管理
```
POST   /api/operators              # 注册经营者
GET    /api/operators/:id          # 获取经营者信息
PUT    /api/operators/:id          # 更新经营者信息
```

#### 产品管理
```
POST   /api/products               # 注册产品
GET    /api/products/:uid          # 通过UID查询产品
PUT    /api/products/:id           # 更新产品信息
DELETE /api/products/:id           # 删除产品
```

#### DPP管理
```
POST   /api/dpp                    # 创建DPP
GET    /api/dpp/:uid               # 通过UID获取DPP（海关查询接口）
PUT    /api/dpp/:id                # 更新DPP
GET    /api/dpp/:id/verify         # 验证DPP完整性
```

#### VC管理
```
POST   /api/vc                     # 上传VC
GET    /api/vc/:productId          # 获取产品相关VC
PUT    /api/vc/:id/status          # 更新VC状态
GET    /api/vc/:id/verify          # 验证VC
```

#### 载体生成
```
POST   /api/carriers/qr            # 生成二维码
POST   /api/carriers/nfc           # 生成NFC数据
GET    /api/carriers/:productId    # 获取产品载体
```

### DPP Registry集成接口
```
POST   /api/registry/register      # 向DPP Registry注册
PUT    /api/registry/update        # 更新Registry信息
GET    /api/registry/status        # 查询注册状态
```

## 部署架构

### 生产环境架构
```
[负载均衡器]
    ↓
[Web服务器集群]
    ↓
[应用服务器集群] ← → [Redis缓存集群]
    ↓
[PostgreSQL主从集群]
    ↓
[文件存储服务]
```

### 容器化部署
```dockerfile
# Dockerfile示例
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### 环境配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=postgresql://...
      - REDIS_URL=redis://...
      - JWT_SECRET=...
      - ECDSA_PRIVATE_KEY=...
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: dppaas
      POSTGRES_USER: dppaas
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
```

## 开发计划

### 第一阶段（3周）- 基础平台
1. **项目初始化**（3天）
   - Next.js + TypeScript项目搭建
   - 数据库设计和Prisma配置
   - 基础认证系统

2. **经营者管理**（4天）
   - EORI号码注册和验证
   - 经营者信息管理
   - 权限控制系统

3. **产品管理**（7天）
   - 产品注册和UID生成
   - 40个核心字段管理
   - 产品信息CRUD操作

4. **基础测试**（3天）
   - 单元测试编写
   - 集成测试
   - API文档生成

### 第二阶段（3周）- 核心功能
1. **DPP生成引擎**（7天）
   - JSON-LD格式生成
   - 数字签名实现
   - 数据完整性验证

2. **VC管理系统**（7天）
   - VC上传和存储
   - VC验证机制
   - 状态管理

3. **载体生成**（3天）
   - 二维码生成
   - NFC数据生成
   - 载体管理

4. **查询接口**（4天）
   - 海关查询接口
   - DPP数据提供
   - 性能优化

### 第三阶段（2周）- 集成部署
1. **DPP Registry集成**（5天）
   - 注册接口对接
   - 数据同步机制
   - 错误处理

2. **生产部署**（4天）
   - 容器化配置
   - 生产环境部署
   - 监控告警配置

3. **测试验收**（5天）
   - 端到端测试
   - 性能测试
   - 安全测试
   - 用户验收测试

## 总结

这个技术栈和架构方案完全基于DPPaaS业务需求设计，涵盖了从经营者注册到海关查询的完整业务流程。采用现代化的技术栈确保开发效率，同时满足欧盟ESPR法规的合规要求。整个MVP开发周期为8周，能够快速上线并满足过审需求。
